"""
Modelo para almacenar recomendaciones de productos a los usuarios.
"""
from sqlalchemy import Column, Integer, Float, DateTime, ForeignKey, Index, PrimaryKeyConstraint, ForeignKeyConstraint
from sqlalchemy.orm import relationship
from datetime import datetime, timezone
from src.db.base import Base
from src.db.models.mixins import TenantMixin, ACCOUNT_ID_FK, ACCOUNT_RANGE


class Recommendation(Base, TenantMixin):
    """Modelo para almacenar recomendaciones de productos a usuarios finales."""

    __tablename__ = "recommendations"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    id = Column(Integer, primary_key=True)

    end_user_id = Column(Integer, nullable=False)
    product_id = Column(Integer, nullable=False)

    score = Column(Float, nullable=False)
    created_at = Column(DateTime(timezone=True), nullable=False, default=lambda: datetime.now(timezone.utc))

    # Relaciones
    account = relationship("Account", back_populates="recommendations")
    end_user = relationship(
        "EndUser",
        foreign_keys=[end_user_id],
        primaryjoin="and_(Recommendation.account_id==EndUser.account_id, Recommendation.end_user_id==EndUser.user_id)",
        back_populates="recommendations"
    )
    product = relationship(
        "Product",
        foreign_keys=[product_id],
        primaryjoin="and_(Recommendation.account_id==Product.account_id, Recommendation.product_id==Product.product_id)",
        back_populates="recommendations"
    )

    # Definición explícita de la PK compuesta e índices
    __table_args__ = (
        PrimaryKeyConstraint("account_id", "id"),
        # Composite FKs for proper tenant isolation
        ForeignKeyConstraint(
            ["account_id", "end_user_id"],
            ["end_users.account_id", "end_users.user_id"],
            ondelete="CASCADE",
            name="fk_recommendation_end_user"
        ),
        ForeignKeyConstraint(
            ["account_id", "product_id"],
            ["products.account_id", "products.product_id"],
            ondelete="CASCADE",
            name="fk_recommendation_product"
        ),
        Index("idx_recommendation_user", "account_id", "end_user_id"),
        Index("idx_recommendation_product", "account_id", "product_id"),
        Index("idx_recommendation_created_at", "created_at"),
        {"postgresql_partition_by": ACCOUNT_RANGE},
    )