from pydantic import BaseModel
from typing import Optional
from datetime import datetime


class EndUserBase(BaseModel):
    external_id: str


class EndUserCreate(EndUserBase):
    pass


class EndUserUpdate(BaseModel):
    external_id: Optional[str] = None
    is_active: Optional[bool] = None


class EndUser(EndUserBase):
    account_id: int
    id: int
    created_at: datetime
    updated_at: datetime
    is_active: bool
    deleted_at: Optional[datetime] = None

    class ConfigDict:
        from_attributes = True
