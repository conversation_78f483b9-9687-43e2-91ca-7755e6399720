from sqlalchemy import (
    <PERSON>umn,
    Integer,
    String,
    DateTime,
    ForeignKey,
    Boolean,
    UniqueConstraint,
    Index,
    func,
    PrimaryKeyConstraint,
    JSON,
    Identity,
)
from sqlalchemy.orm import relationship
from src.db.base import Base


class EndUser(Base):
    __tablename__ = "end_users"

    user_id = Column(Integer, Identity(), primary_key=True)
    account_id = Column(Integer, ForeignKey("accounts.account_id"), primary_key=True)
    created_at = Column(DateTime(timezone=True), nullable=True)
    updated_at = Column(DateTime(timezone=True), nullable=True)
    last_activity_at = Column(
        DateTime(timezone=True),
        nullable=True,
        comment="Timestamp of last activity by this end user",
    )

    __table_args__ = (PrimaryKeyConstraint("user_id", "account_id"),)

    # Relationships
    account = relationship("Account", back_populates="end_users")
    interactions = relationship(
        "Interaction",
        back_populates="end_user",
    )
    recommendations = relationship(
        "Recommendation",
        back_populates="end_user",
    )
    searches = relationship("Search", back_populates="end_user")
    orders = relationship("Order", back_populates="end_user")
