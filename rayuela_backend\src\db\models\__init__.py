"""
Este archivo asegura que todos los modelos se importen en el orden correcto
para evitar dependencias circulares.
"""

# Importaciones de SQLAlchemy
from sqlalchemy import (
    Column,
    Integer,
    String,
    Boolean,
    DateTime,
    ForeignKey,
    Text,
    Enum,
    DECIMAL,
    JSON,
)
from sqlalchemy.orm import relationship
from datetime import datetime
from src.db.enums import (
    NotificationType,
    InteractionType,
    RoleType,
    PermissionType,
    SubscriptionPlan,
    TrainingJobStatus,
    BatchIngestionJobStatus,
    OrderStatus,
)

# Importar Base desde base.py
from src.db.base import Base

# Primero importamos los modelos base que no tienen dependencias
from .account import Account
from .mixins import TenantMixin

# Luego los modelos que dependen de Account
from .subscription import Subscription
from .account_usage_metrics import AccountUsageMetrics, EndpointMetrics
from .notification import Notification
from .audit_log import AuditLog

# Luego los modelos de usuarios y roles
from .system_user import SystemUser
from .role import Role, role_permissions  # Importamos la tabla de asociación
from .permission import Permission

# Ya no importamos RolePermission como clase, ya está importado role_permissions
from .system_user_role import SystemUserRole  # Importamos solo la clase

# Finalmente los modelos de datos y ML
from .end_user import EndUser
from .product import Product
from .interaction import Interaction
from .search import Search
from .recommendation import Recommendation
from .model_metadata import ModelMetadata
from .model_metric import ModelMetric

# ModelMetrics ya no se importa porque se ha consolidado con ModelMetric
from .training_metrics import TrainingMetrics
from .training_job import TrainingJob
from .batch_ingestion_job import BatchIngestionJob
from .order import Order
from .order_item import OrderItem

__all__ = [
    # Clases base
    "Base",
    "TenantMixin",
    # Modelos
    "Account",
    "Subscription",
    "AccountUsageMetrics",
    "EndpointMetrics",
    "Notification",
    "AuditLog",
    "SystemUser",
    "Role",
    "Permission",
    "role_permissions",  # Exportamos la tabla de asociación
    # Ya no exportamos "RolePermission" como clase
    "SystemUserRole",  # Exportamos la clase
    "EndUser",
    "Product",
    "Interaction",
    "Search",
    "Recommendation",
    "ModelMetadata",
    "ModelMetric",
    # "ModelMetrics",  # Ya no se exporta porque se ha consolidado con ModelMetric
    "TrainingMetrics",
    "TrainingJob",
    "BatchIngestionJob",
    "Order",
    "OrderItem",
    # Tipos SQLAlchemy
    "Column",
    "Integer",
    "String",
    "Boolean",
    "DateTime",
    "ForeignKey",
    "Text",
    "Enum",
    "DECIMAL",
    "JSON",
    "relationship",
    # Enums
    "NotificationType",
    "InteractionType",
    "RoleType",
    "PermissionType",
    "SubscriptionPlan",
    "TrainingJobStatus",
    "BatchIngestionJobStatus",
    "OrderStatus",
    # Utilidades
    "datetime",
]
