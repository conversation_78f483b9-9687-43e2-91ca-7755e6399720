from sqlalchemy import (
    Column,
    Integer,
    DateTime,
    ForeignKey,
    Text,
    Index,
    func,
    ForeignKeyConstraint,
    PrimaryKeyConstraint,
)
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import TenantMixin, ACCOUNT_ID_FK, ACCOUNT_RANGE


class Search(Base, TenantMixin):
    __tablename__ = "searches"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    id = Column(Integer, primary_key=True)
    end_user_id = Column(Integer, nullable=False)
    query = Column(Text)
    timestamp = Column(DateTime(timezone=True), server_default=func.now())

    __table_args__ = (
        PrimaryKeyConstraint("account_id", "id"),
        # Composite FK for proper tenant isolation
        ForeignKeyConstraint(
            ["account_id", "end_user_id"],
            ["end_users.account_id", "end_users.user_id"],
            ondelete="CASCADE",
            name="fk_search_end_user"
        ),
        Index("idx_search_account_user", "account_id", "end_user_id"),
        Index("idx_search_account_timestamp", "account_id", "timestamp"),
        {"postgresql_partition_by": ACCOUNT_RANGE},
    )

    # Relationships
    account = relationship("Account", back_populates="searches")
    end_user = relationship(
        "EndUser",
        foreign_keys=[end_user_id],
        primaryjoin="and_(Search.account_id==EndUser.account_id, Search.end_user_id==EndUser.user_id)",
        back_populates="searches"
    )
