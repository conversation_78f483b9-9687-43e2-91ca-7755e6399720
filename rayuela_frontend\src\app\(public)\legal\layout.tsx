"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { ArrowLeft, FileText, Shield } from "lucide-react";

export default function LegalLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto py-8 px-4">
        <div className="mb-6">
          <Link 
            href="/" 
            className="inline-flex items-center text-sm text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-200"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Volver al inicio
          </Link>
        </div>

        <div className="flex flex-col md:flex-row gap-8">
          <div className="w-full md:w-64 shrink-0">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-4 sticky top-8">
              <h3 className="font-medium text-lg mb-4">Documentos Legales</h3>
              <nav className="space-y-2">
                <Link 
                  href="/legal/terms" 
                  className={`flex items-center p-2 rounded-md ${
                    pathname === "/legal/terms" 
                      ? "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300" 
                      : "hover:bg-gray-100 dark:hover:bg-gray-700"
                  }`}
                >
                  <FileText className="mr-2 h-4 w-4" />
                  Términos y Condiciones
                </Link>
                <Link 
                  href="/legal/privacy" 
                  className={`flex items-center p-2 rounded-md ${
                    pathname === "/legal/privacy" 
                      ? "bg-blue-50 text-blue-700 dark:bg-blue-900/30 dark:text-blue-300" 
                      : "hover:bg-gray-100 dark:hover:bg-gray-700"
                  }`}
                >
                  <Shield className="mr-2 h-4 w-4" />
                  Política de Privacidad
                </Link>
              </nav>
            </div>
          </div>

          <div className="flex-1">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}
