// src/lib/useApiKeys.ts
import useSWR, { SWRResponse } from 'swr';
import {
  getApiKey,
  createApiKey,
  NewApiKeyResponse,
  ApiKey,
  ApiError
} from '@/lib/api';
import { useAuth } from '@/lib/auth';
import { useState } from 'react';

interface ApiKeyState {
  data: ApiKey | null;
  error: ApiError | null;
  isLoading: boolean;
  isValidating: boolean;
  mutate: () => Promise<ApiKey | undefined>;
  dataUpdatedAt: number;
}

interface ApiKeyOperations {
  regenerateApiKey: () => Promise<NewApiKeyResponse | null>;
  isRegenerating: boolean;
  operationError: ApiError | null;
  getFormattedApiKey: () => string | null;
}

/**
 * Hook para gestionar la API Key del usuario.
 *
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos de la API Key, estado de carga, errores y funciones de utilidad
 */
export function useApiKeys(options: {
  revalidateOnFocus?: boolean;
  refreshInterval?: number;
  dedupingInterval?: number;
  errorRetryCount?: number;
} = {}): ApiKeyState & ApiKeyOperations {
  const { token, apiKey } = useAuth();
  const [isRegenerating, setIsRegenerating] = useState(false);
  const [operationError, setOperationError] = useState<ApiError | null>(null);

  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt
  } = useSWR<ApiKey, ApiError>(
    token && apiKey ? ['api-key', token, apiKey] : null,
    async ([_, tokenValue, apiKeyValue]) => await getApiKey(tokenValue, apiKeyValue),
    {
      revalidateOnFocus: options.revalidateOnFocus ?? true,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 60000,
      errorRetryCount: options.errorRetryCount ?? 3,
      onError: (err) => {
        if (ApiError.isApiError(err)) {
          console.error('Error fetching API key:', err.message, err.details);
        } else {
          console.error('Unknown error fetching API key:', err);
        }
      }
    }
  );

  /**
   * Regenera la API Key
   * @returns La nueva API Key generada o null si ocurre un error
   */
  const regenerateKey = async (): Promise<NewApiKeyResponse | null> => {
    if (!token || !apiKey) {
      setOperationError(new ApiError(
        'No hay token o API Key disponible',
        401,
        'AUTH_REQUIRED',
        null
      ));
      return null;
    }

    setIsRegenerating(true);
    setOperationError(null);

    try {
      const response = await createApiKey(token, apiKey);
      await mutate();
      return response;
    } catch (err) {
      const apiError = ApiError.isApiError(err) ? err : new ApiError(
        'Error al regenerar API Key',
        500,
        'REGENERATE_API_KEY_ERROR',
        null
      );
      setOperationError(apiError);
      return null;
    } finally {
      setIsRegenerating(false);
    }
  };

  /**
   * Formatea la API Key para mostrar (prefijo + asteriscos + sufijo)
   * @returns API Key formateada o null si no se encuentra
   */
  const getFormattedApiKey = (): string | null => {
    if (!data || !data.prefix || !data.last_chars) return null;
    return `${data.prefix}••••••••${data.last_chars}`;
  };

  return {
    data: data ?? null,
    error: error ?? null,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt: dataUpdatedAt ?? 0,
    regenerateApiKey: regenerateKey,
    isRegenerating,
    operationError,
    getFormattedApiKey
  };
}
