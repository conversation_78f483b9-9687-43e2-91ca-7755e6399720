"""
Repositorios para la gestión de cuentas y suscripciones.
"""

from sqlalchemy import select, func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from datetime import datetime
from fastapi import HTTPException
from src.db import models, schemas
from .base import BaseRepository
from src.utils.base_logger import log_info, log_error


class AccountRepository(
    BaseRepository[models.Account, schemas.AccountCreate, schemas.AccountUpdate]
):
    """Repositorio para la gestión de cuentas"""

    def __init__(self, db: AsyncSession):
        super().__init__(db, model=models.Account)

    async def create(self, account_create: schemas.AccountCreate) -> models.Account:
        """
        Crea una nueva cuenta utilizando la columna Identity para generar el ID.

        Args:
            account_create: Datos para crear la cuenta

        Returns:
            La cuenta creada

        Raises:
            SQLAlchemyError: Si hay un error en la base de datos
        """
        try:
            # Crear objeto Account sin especificar el ID (se generará automáticamente)
            account = models.Account(
                name=account_create.name,
                api_key_hash=account_create.api_key_hash,
                api_key_prefix=account_create.api_key_prefix,
                api_key_last_chars=account_create.api_key_last_chars,
                api_key_created_at=account_create.api_key_created_at,
                api_key_revealed=account_create.api_key_revealed,
                is_active=account_create.is_active,
            )

            self.db.add(account)
            # Hacer flush para obtener el ID generado
            await self.db.flush()

            log_info(f"Account created with auto-generated ID {account.account_id}")
            return account
        except SQLAlchemyError as e:
            log_error(f"Error creating account: {e}")
            raise  # Dejar que el gestor de transacciones maneje el rollback

    async def execute_with_partition(self, query):
        """Ejecuta una consulta filtrada por account_id"""
        query = self._add_tenant_filter(query)
        result = await self.db.execute(query)
        return result

    async def get_by_id(self, id: int) -> Optional[models.Account]:
        """Obtener cuenta por ID"""
        try:
            result = await self.db.execute(
                select(models.Account).filter(models.Account.account_id == id)
            )
            account = result.scalars().first()
            if not account:
                raise HTTPException(status_code=404, detail="Account not found")
            return account
        except SQLAlchemyError as e:
            await self._handle_error("fetching account", e)

    async def get_by_api_key(self, api_key: str) -> Optional[models.Account]:
        """Obtén cuenta por API key usando comparación de tiempo constante.

        Args:
            api_key: La API key en texto plano proporcionada por el usuario

        Returns:
            La cuenta si la API key es válida, None en caso contrario
        """
        try:
            from src.core.security.api_key import verify_api_key

            # Get all active accounts with API keys (without filtering by hash to prevent timing attacks)
            query = select(models.Account).filter(
                models.Account.is_active == True,
                models.Account.deleted_at.is_(None),
                models.Account.api_key_hash.is_not(None)
            )
            result = await self.execute_with_partition(query)
            accounts = result.scalars().all()

            # Use timing-safe comparison to find matching account
            for account in accounts:
                if verify_api_key(api_key, account.api_key_hash):
                    return account

            return None
        except SQLAlchemyError as e:
            await self._handle_error("fetching account by API key", e)

    async def get_by_stripe_customer_id(self, stripe_customer_id: str) -> Optional[models.Account]:
        """Obtén cuenta por ID de cliente de Stripe.

        Args:
            stripe_customer_id: ID del cliente en Stripe

        Returns:
            La cuenta si se encuentra, None en caso contrario
        """
        try:
            query = select(models.Account).filter(
                models.Account.stripe_customer_id == stripe_customer_id
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            await self._handle_error("fetching account by Stripe customer ID", e)

    async def get_all(
        self, skip: int = 0, limit: int = 100, is_active: Optional[bool] = None
    ) -> List[models.Account]:
        """Obtener todas las cuentas con filtros opcionales"""
        try:
            query = select(models.Account)
            if is_active is not None:
                query = query.filter(models.Account.is_active == is_active)
            query = query.offset(skip).limit(limit)
            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching accounts", e)

    async def update(
        self, id: int, account_update: schemas.AccountUpdate
    ) -> Optional[models.Account]:
        """Actualizar una cuenta existente"""
        try:
            account = await self.get_by_id(id)
            if not account:
                raise HTTPException(status_code=404, detail="Account not found")

            for key, value in account_update.model_dump(exclude_unset=True).items():
                setattr(account, key, value)

            await self.db.refresh(account)
            return account
        except SQLAlchemyError as e:
            await self._handle_error("updating account", e)

    async def delete(self, id: int) -> bool:
        """Eliminar una cuenta (soft delete)"""
        try:
            account = await self.get_by_id(id)
            if not account:
                raise HTTPException(status_code=404, detail="Account not found")

            account.is_active = False
            return True
        except SQLAlchemyError as e:
            await self._handle_error("deleting account", e)
            return False

    # Business logic methods have been moved to the AccountService and SubscriptionService


class SubscriptionRepository(BaseRepository[models.Subscription, schemas.SubscriptionCreate, schemas.SubscriptionUpdate]):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.Subscription)

    async def create(
        self, subscription_create: schemas.SubscriptionCreate
    ) -> models.Subscription:
        try:
            subscription = models.Subscription(
                **subscription_create.model_dump(), account_id=self.account_id
            )
            self.db.add(subscription)
            await self.db.refresh(subscription)
            return subscription
        except SQLAlchemyError as e:
            await self._handle_error("creating subscription", e)

    async def get_by_account(self, account_id: int) -> Optional[models.Subscription]:
        try:
            query = select(models.Subscription).filter(
                models.Subscription.account_id == account_id,
                models.Subscription.is_active.is_(True),
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            await self._handle_error("fetching subscription", e)

    # Business logic methods have been moved to the SubscriptionService

    # Los métodos update_usage_metrics y reset_usage_metrics han sido movidos a SubscriptionService

    async def delete(self, subscription_id: int) -> None:
        """Elimina una suscripción por ID."""
        try:
            subscription = await self.get_by_id(subscription_id)
            if subscription:
                await self.db.delete(subscription)
        except SQLAlchemyError as e:
            await self._handle_error("deleting subscription", e)

    async def get_all(self) -> List[models.Subscription]:
        """Obtiene todas las suscripciones."""
        try:
            query = select(models.Subscription)
            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching all subscriptions", e)

    async def update(
        self, subscription_id: int, update_data: Dict[str, Any]
    ) -> Optional[models.Subscription]:
        """Actualiza una suscripción existente."""
        try:
            subscription = await self.get_by_id(subscription_id)
            if not subscription:
                raise HTTPException(status_code=404, detail="Subscription not found")

            for key, value in update_data.items():
                setattr(subscription, key, value)

            await self.db.refresh(subscription)
            return subscription
        except SQLAlchemyError as e:
            await self._handle_error("updating subscription", e)
