"""
Esquemas Pydantic para API Keys.
"""
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, ConfigDict

class ApiKeyBase(BaseModel):
    """Base schema for API Keys"""
    name: Optional[str] = Field(None, description="Nombre descriptivo para esta API Key")
    
class ApiKeyCreate(ApiKeyBase):
    """Schema for creating a new API Key"""
    pass
    
class ApiKeyResponse(ApiKeyBase):
    """Schema for API Key information (no sensitive data)"""
    prefix: Optional[str] = Field(None, description="Prefijo de la API Key")
    last_chars: Optional[str] = Field(None, description="Últimos caracteres de la API Key")
    created_at: Optional[datetime] = Field(None, description="Fecha de creación")
    is_active: bool = Field(True, description="Estado de la API Key")
    last_used: Optional[datetime] = Field(None, description="Último uso de la API Key")
    
    model_config = ConfigDict(from_attributes=True)
        
class NewApiKeyResponse(BaseModel):
    """Schema for a newly created API Key (includes the full key)"""
    api_key: str = Field(..., description="API Key completa (solo se muestra una vez)")
    prefix: str = Field(..., description="Prefijo de la API Key")
    created_at: datetime = Field(..., description="Fecha de creación")
    message: str = Field(..., description="Mensaje informativo") 