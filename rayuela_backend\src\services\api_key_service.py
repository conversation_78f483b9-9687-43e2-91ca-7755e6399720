"""
Servicio para manejar la lógica de API Keys.
"""
from datetime import datetime, timezone
from typing import Op<PERSON>, <PERSON><PERSON>, Dict, Any
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.security.api_key import generate_api_key, hash_api_key
from src.db.models import Account
from src.db.repositories import AccountRepository
from src.utils.base_logger import log_info, log_error

class ApiKeyService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.account_repo = AccountRepository(db)
        
    async def generate_first_api_key(
        self,
        account: Account
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Genera la primera API Key para una cuenta.
        Retorna la API Key y los datos para actualizar la cuenta.
        """
        try:
            # Generar API Key y su hash
            api_key, api_key_hash = generate_api_key()
            api_key_parts = api_key.split("_")
            
            # Preparar datos de actualización
            update_data = {
                'api_key_hash': api_key_hash,
                'api_key_prefix': api_key_parts[0] if len(api_key_parts) > 0 else "",
                'api_key_last_chars': api_key[-6:] if len(api_key) >= 6 else api_key,
                'api_key_created_at': datetime.now(timezone.utc),
                'api_key_revealed': True
            }
            
            return api_key, update_data
            
        except Exception as e:
            log_error(f"Error generating first API Key: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error generating API Key"
            )
            
    async def validate_api_key(
        self,
        api_key: str,
        account: Account
    ) -> bool:
        """
        Valida una API Key contra una cuenta usando comparación de tiempo constante.
        """
        try:
            if not getattr(account, 'api_key_hash', None):
                return False

            # Use timing-safe comparison to prevent timing attacks
            from src.core.security.api_key import verify_api_key
            return verify_api_key(api_key, account.api_key_hash)

        except Exception as e:
            log_error(f"Error validating API Key: {str(e)}")
            return False
            
    async def rotate_api_key(
        self,
        account: Account
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Genera una nueva API Key para una cuenta existente.
        """
        try:
            # Generar nueva API Key
            api_key, update_data = await self.generate_first_api_key(account)
            
            # Actualizar cuenta
            await self.account_repo.update(account, update_data)
            
            return api_key, update_data
            
        except Exception as e:
            log_error(f"Error rotating API Key: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error rotating API Key"
            ) 