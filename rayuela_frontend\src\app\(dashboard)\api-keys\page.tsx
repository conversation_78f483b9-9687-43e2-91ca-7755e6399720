// src/app/(dashboard)/api-keys/page.tsx
"use client";

import { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from '@/lib/auth';
import { useApiKeys } from '@/lib/useApiKeys';
import { revokeApiKey } from '@/lib/api';
import { toast } from "sonner";
import { handleApiError } from "@/lib/error-handler";
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { AlertCircle, RefreshCw } from 'lucide-react';
import Link from 'next/link';
import InitialApiKeyModal from '@/components/auth/InitialApiKeyModal';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function ApiKeysPage() {
  const { token, apiKey, user, isLoading: isAuthLoading } = useAuth();
  const [showNewKeyModal, setShowNewKeyModal] = useState(false);
  const [newApiKey, setNewApiKey] = useState<string | null>(null);

  const {
    data: apiKeyData,
    error: apiKeyError,
    isLoading: isApiKeyLoading,
    isRegenerating,
    regenerateApiKey,
    getFormattedApiKey
  } = useApiKeys({
    revalidateOnFocus: true,
    refreshInterval: 30000, // Actualizar cada 30 segundos
    dedupingInterval: 5000, // Evitar peticiones duplicadas en 5 segundos
    errorRetryCount: 3
  });

  const handleRegenerateKey = async () => {
    try {
      const result = await regenerateApiKey();
      if (result?.api_key) {
        setNewApiKey(result.api_key);
        setShowNewKeyModal(true);
        toast.success("API Key regenerada con éxito");
      }
    } catch (error: any) {
      handleApiError(error, "Error al regenerar la API Key");
    }
  };

  const handleRevokeKey = async () => {
    try {
      if (!token || !apiKey) {
        toast.error("No se pudo revocar la API Key: No hay sesión activa");
        return;
      }

      await revokeApiKey(token, apiKey);
      toast.success("API Key revocada con éxito");

      // Recargar la página para actualizar la sesión
      window.location.reload();
    } catch (error: any) {
      handleApiError(error, "Error al revocar la API Key");
    }
  };

  const handleModalClose = () => {
    setShowNewKeyModal(false);
  };

  // Estado de carga combinado
  const isLoading = isAuthLoading || isApiKeyLoading;

  // Estado de Error (Auth o API Key)
  if (apiKeyError) {
    return (
      <div className="text-red-600">
        <h1 className="text-2xl font-semibold mb-4">API Key</h1>
        <p>Error al cargar la API Key: {apiKeyError.message}</p>
        <p>Intenta refrescar la página o contacta a soporte.</p>
      </div>
    );
  }

  // Estado: No autenticado
  if (!token || !user) {
    return (
      <div className="text-orange-600">
        <h1 className="text-2xl font-semibold mb-4">API Key</h1>
        <p>No se pudo verificar tu sesión. Por favor, intenta <Link href="/login" className="underline">iniciar sesión</Link> de nuevo.</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-semibold">API Key</h1>
        <div className="flex gap-2">
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline" className="text-red-600 border-red-200 hover:bg-red-50 hover:text-red-700">
                <AlertCircle className="h-4 w-4 mr-2" />
                Revocar API Key
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>¿Revocar API Key?</AlertDialogTitle>
                <AlertDialogDescription>
                  Esta acción eliminará permanentemente tu API Key actual.
                  Cualquier aplicación o servicio que esté usando esta API Key dejará de funcionar.
                  Después de revocarla, tendrás que generar una nueva API Key.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleRevokeKey}
                  className="bg-red-600 hover:bg-red-700"
                >
                  Revocar
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button variant="outline">
                <RefreshCw className="h-4 w-4 mr-2" />
                Regenerar API Key
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>¿Regenerar API Key?</AlertDialogTitle>
                <AlertDialogDescription>
                  Esta acción invalidará tu API Key actual y generará una nueva.
                  Cualquier aplicación o servicio que esté usando la API Key actual dejará de funcionar.
                  Asegúrate de actualizar todas las aplicaciones con la nueva API Key.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleRegenerateKey}
                  disabled={isRegenerating}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  {isRegenerating ? 'Regenerando...' : 'Sí, regenerar API Key'}
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {isLoading ? (
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-1/2" />
            <Skeleton className="h-4 w-3/4 mt-2" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Tu API Key</CardTitle>
            <CardDescription>
              Esta es tu API Key actual. Úsala para autenticar tus solicitudes a la API.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between p-3 bg-muted/50 dark:bg-muted/30 rounded border">
              <span className="font-mono text-sm">{getFormattedApiKey()}</span>
              <span className="text-xs text-muted-foreground">
                {apiKeyData?.is_active ? 'Activa' : 'Inactiva'}
              </span>
            </div>
            {apiKeyData?.created_at && (
              <p className="text-sm text-muted-foreground mt-2">
                Creada el {format(new Date(apiKeyData.created_at), 'PPP', { locale: es })}
                {apiKeyData.last_used && (
                  <> • Último uso: {format(new Date(apiKeyData.last_used), 'PPP', { locale: es })}</>
                )}
              </p>
            )}

            <div className="mt-4 p-4 border rounded-md bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-700">
              <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-2 flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                Información importante sobre tu API Key
              </h3>
              <div className="space-y-3 text-sm text-amber-700 dark:text-amber-400">
                <p>
                  Por razones de seguridad, tu API Key completa <strong>solo se muestra una vez</strong> al momento de crearla. 
                  Es crucial que la copies y almacenes en un lugar seguro inmediatamente.
                </p>
                <p className="font-medium">
                  Si has perdido tu API Key actual, puedes:
                </p>
                <ol className="list-decimal list-inside space-y-1 pl-2">
                  <li>Usar el botón <strong>"Regenerar API Key"</strong> arriba para crear una nueva.</li>
                  <li>Copiar la nueva API Key cuando aparezca.</li>
                  <li>Actualizar todas tus integraciones con la nueva clave.</li>
                </ol>
                <p className="font-bold text-amber-800 dark:text-amber-300">
                  Nota: Regenerar tu API Key invalidará la clave anterior. Todas las aplicaciones que usen la clave anterior dejarán de funcionar.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Modal para mostrar la nueva API Key */}
      {showNewKeyModal && newApiKey && (
        <InitialApiKeyModal
          apiKey={newApiKey}
          onClose={handleModalClose}
        />
      )}
    </div>
  );
}