"""
Endpoints para la gestión de API Keys.
"""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.db import models
from src.db.session import get_db
from src.db.schemas.api_key import ApiKeyResponse, NewApiKeyResponse
from src.core.deps import get_current_account
from src.services.api_key_service import ApiKeyService

router = APIRouter()


@router.post("/", response_model=NewApiKeyResponse, status_code=status.HTTP_201_CREATED)
async def create_api_key(
    current_account: models.Account = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Genera una nueva API Key para usuarios autenticados.

    Este endpoint permite a usuarios que ya tienen una cuenta generar una nueva API Key.
    Esto es útil cuando:
    - Perdiste tu API Key original
    - Necesitas rotar tu API Key por seguridad
    - Eres un usuario existente que necesita una API Key

    **IMPORTANTE**:
    - Requiere autenticación JWT (debes estar logueado)
    - La API Key completa solo se devolverá una vez
    - La API Key anterior será invalidada automáticamente
    - Si la pierdes, tendrás que generar una nueva usando este mismo endpoint

    **Autenticación requerida**: JWT token en el header Authorization: Bearer <token>

    Returns:
    - **api_key**: Tu nueva API Key completa (solo se muestra una vez)
    - **prefix**: Prefijo de la API Key para identificación
    - **created_at**: Fecha y hora de creación
    - **message**: Mensaje informativo sobre el uso seguro
    """
    try:
        api_key_service = ApiKeyService(db)
        api_key, update_data = await api_key_service.rotate_api_key(current_account)

        return {
            "api_key": api_key,
            "prefix": update_data.get("api_key_prefix", ""),
            "created_at": update_data.get("api_key_created_at"),
            "message": "Esta es tu nueva API Key. Guárdala en un lugar seguro, solo se mostrará una vez.",
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al generar la API Key: {str(e)}",
        )


@router.get("/current", response_model=ApiKeyResponse)
async def get_current_api_key(
    current_account: models.Account = Depends(get_current_account),
):
    """
    Obtiene información sobre la API Key actual.
    No devuelve la API Key completa, solo metadatos como:
    - Prefijo de la API Key
    - Últimos caracteres
    - Fecha de creación
    - Último uso (si está disponible)

    Esta información permite identificar la API Key sin comprometer seguridad.
    """
    try:
        # Obtener información de la API Key desde la cuenta actual
        return {
            "prefix": getattr(current_account, "api_key_prefix", None),
            "last_chars": getattr(current_account, "api_key_last_chars", None),
            "created_at": getattr(current_account, "api_key_created_at", None),
            "is_active": (
                True if getattr(current_account, "api_key_hash", None) else False
            ),
            "last_used": getattr(current_account, "api_key_last_used", None),
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al obtener información de la API Key: {str(e)}",
        )


@router.delete("/", status_code=status.HTTP_204_NO_CONTENT)
async def revoke_api_key(
    current_account: models.Account = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """
    Revoca la API Key actual.
    Esta acción es irreversible. Después de revocar, tendrás que generar una nueva API Key.
    """
    try:
        # Limpiar los campos de API Key en la cuenta
        # Actualizar directamente el objeto de la sesión
        current_account.api_key_hash = None
        current_account.api_key_prefix = None
        current_account.api_key_last_chars = None
        current_account.api_key_revealed = False

        # Commit los cambios
        await db.commit()

        return None
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error al revocar la API Key: {str(e)}",
        )
