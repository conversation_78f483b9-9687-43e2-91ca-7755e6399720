// src/app/(public)/home/<USER>
"use client";

import { useState, useEffect } from 'react';
import Link from 'next/link';
import React from 'react';
import { Button } from '@/components/ui/button';

export default function HomePage() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => {
      setIsLoaded(true);
    }, 100);
    return () => clearTimeout(timeout);
  }, []);

  return React.createElement('div', {
    className: "min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800"
  },
    React.createElement('div', {
      className: "container mx-auto px-4 py-20 text-center"
    },
      React.createElement('h1', {
        className: "text-4xl font-bold text-gray-900 dark:text-white mb-6"
      }, "Bienvenido a Rayuela"),
      React.createElement('p', {
        className: "text-xl text-gray-600 dark:text-gray-300 mb-8"
      }, "Sistema de recomendaciones inteligentes para tu negocio"),
      React.createElement('div', {
        className: "flex justify-center gap-4"
      },
        React.createElement(Button, {
          asChild: true
        }, React.createElement(Link, { href: "/register" }, "Comenzar Gratis")),
        React.createElement(Button, {
          variant: "outline",
          asChild: true
        }, React.createElement(Link, { href: "/login" }, "Iniciar Sesión"))
      )
    )
  );
}
