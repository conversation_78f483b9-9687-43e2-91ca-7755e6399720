# Resumen de Unificación de Nomenclatura: `user_id` vs `end_user_id`

## Problema Identificado

Se detectó una **inconsistencia crítica** en la nomenclatura de IDs de usuario en la API de Rayuela:

### Inconsistencias Encontradas:
1. **En rutas de URL**: Se usaba `end_user_id` (ej: `/invalidate-cache/{end_user_id}`, `/explain/{end_user_id}/{item_id}`)
2. **En cuerpos de request**: Se usaba `user_id` (ej: `RecommendationQueryRequest.user_id`)
3. **En modelos de base de datos**: Se usaba `end_user_id` (ej: `Interaction.end_user_id`, `Recommendation.end_user_id`)
4. **En esquemas**: Mezc<PERSON> de ambos (`InteractionBase.end_user_id` vs `RecommendationQueryRequest.user_id`)

## Estrategia de Solución

Se decidió **unificar toda la nomenclatura a `user_id`** para mantener consistencia y simplicidad, siguiendo estas reglas:

1. **Mantener `system_user_id`** intacto para evitar confusión con usuarios del sistema
2. **Cambiar `end_user_id` a `user_id`** en todos los contextos de usuario final
3. **Implementar mapeo temporal** para manejar la transición gradual
4. **Actualizar rutas, esquemas y servicios** de forma sistemática

## Cambios Implementados

### 1. Endpoints de API (`rayuela_backend/src/api/v1/endpoints/`)

#### `recommendations.py`
- ✅ **Cambiado**: `/invalidate-cache/{end_user_id}` → `/invalidate-cache/{user_id}`
- ✅ **Cambiado**: `/explain/{end_user_id}/{item_id}` → `/explain/{user_id}/{item_id}`
- ✅ **Actualizado**: Parámetros de función y documentación
- ✅ **Actualizado**: Referencias internas en el código

#### `users.py`
- ✅ **Cambiado**: `/{end_user_id}` → `/{user_id}`
- ✅ **Actualizado**: Parámetros de función

### 2. Esquemas (`rayuela_backend/src/db/schemas/`)

#### `interaction.py`
- ✅ **Cambiado**: `InteractionBase.end_user_id` → `InteractionBase.user_id`

#### `search.py`
- ✅ **Cambiado**: `SearchBase.end_user_id` → `SearchBase.user_id`

### 3. Utilidades de Mapeo

#### `rayuela_backend/src/utils/user_id_mapper.py` (NUEVO)
- ✅ **Creado**: Funciones para mapear entre `end_user_id` y `user_id`
- ✅ **Implementado**: `UserIdMapper` class para conversiones
- ✅ **Funciones disponibles**:
  - `map_end_user_id_to_user_id()`
  - `map_user_id_to_end_user_id()`
  - `extract_user_id_from_request_data()`
  - `normalize_user_id_field()`

### 4. Servicios Actualizados

#### `interaction_service.py`
- ✅ **Integrado**: Mapeo automático de `user_id` a `end_user_id` para compatibilidad con BD
- ✅ **Actualizado**: Manejo de datos de interacción con mapeo transparente

### 5. Scripts de Migración

#### `rayuela_backend/scripts/migrate_user_id_nomenclature.py` (NUEVO)
- ✅ **Creado**: Script para generar migraciones de Alembic automáticamente
- ✅ **Incluye**: Migración completa de columnas, índices y foreign keys
- ✅ **Incluye**: Función de rollback para revertir cambios si es necesario
- ✅ **Incluye**: Instrucciones detalladas para aplicar la migración

## Estado Actual

### ✅ Completado
- [x] Endpoints de recomendaciones unificados
- [x] Endpoint de usuarios unificado
- [x] Esquemas de interacción y búsqueda actualizados
- [x] Utilidad de mapeo implementada
- [x] Servicio de interacciones actualizado

### 🔄 Pendiente (Requiere Migraciones de BD)
- [ ] Modelos de base de datos (`Interaction.end_user_id`, `Recommendation.end_user_id`)
- [ ] Migraciones de Alembic para renombrar columnas
- [ ] Actualización de índices de base de datos
- [ ] Actualización de foreign keys

### 📋 Próximos Pasos Recomendados

1. **Ejecutar script de migración**:
   ```bash
   cd rayuela_backend
   python scripts/migrate_user_id_nomenclature.py
   ```

2. **Completar migración de base de datos**:
   - Editar el archivo de migración generado
   - Reemplazar `[PREVIOUS_REVISION]` con la revisión anterior
   - Aplicar migración: `alembic upgrade head`

3. **Actualizar modelos restantes**:
   - `src/db/models/interaction.py`
   - `src/db/models/recommendation.py`
   - `src/db/models/search.py`

4. **Actualizar repositorios**:
   - `src/db/repositories/interaction.py`
   - `src/db/repositories/user.py`

5. **Actualizar servicios ML**:
   - `src/ml_pipeline/evaluation.py`
   - `src/ml_pipeline/serving_engine.py`

6. **Actualizar tests**:
   - Todos los tests que usen `end_user_id`

7. **Verificar funcionalidad**:
   - Ejecutar tests de integración
   - Verificar endpoints en ambiente de desarrollo

## Beneficios Logrados

### 🎯 Experiencia del Desarrollador (DX)
- **Consistencia**: Una sola convención (`user_id`) en toda la API
- **Claridad**: Eliminación de confusión entre `user_id` y `end_user_id`
- **Simplicidad**: Menos variaciones de nombres para recordar

### 🔧 Mantenibilidad
- **Código más limpio**: Menos mapeos manuales necesarios
- **Documentación clara**: APIs más fáciles de documentar y entender
- **Menos errores**: Reducción de bugs por inconsistencias de nomenclatura

### 🚀 Integración
- **APIs más intuitivas**: Los integradores pueden usar `user_id` consistentemente
- **Menos fricción**: Reducción de tiempo de onboarding para nuevos desarrolladores

## Compatibilidad Durante la Transición

El `UserIdMapper` permite mantener compatibilidad durante la migración:

```python
# Conversión automática en servicios
from src.utils.user_id_mapper import UserIdMapper

# API → BD
db_data = UserIdMapper.to_db_format(api_data)

# BD → API  
api_data = UserIdMapper.to_api_format(db_data)

# Extracción flexible
user_id = UserIdMapper.extract_user_id(request_data)
```

## Impacto en Integradores

### ✅ Cambios Necesarios para Integradores

1. **Actualizar rutas de endpoints**:
   - `/invalidate-cache/{end_user_id}` → `/invalidate-cache/{user_id}`
   - `/explain/{end_user_id}/{item_id}` → `/explain/{user_id}/{item_id}`
   - `/users/{end_user_id}` → `/users/{user_id}`

2. **Actualizar esquemas de datos**:
   - `InteractionCreate.end_user_id` → `InteractionCreate.user_id`
   - `SearchCreate.end_user_id` → `SearchCreate.user_id`

### 🔄 Sin Cambios Requeridos

- `RecommendationQueryRequest.user_id` (ya usaba la nomenclatura correcta)
- Endpoints que no involucran IDs de usuario

## Conclusión

La unificación de nomenclatura a `user_id` representa una **mejora crítica** en la experiencia del desarrollador y la consistencia de la API. Los cambios implementados eliminan la principal fuente de confusión identificada y establecen una base sólida para futuras integraciones.

La estrategia de mapeo temporal permite una transición suave sin romper la funcionalidad existente, mientras que los próximos pasos completarán la unificación a nivel de base de datos. 